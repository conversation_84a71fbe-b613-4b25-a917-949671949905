<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\RoleMenuPermissionAssignRequest;
use App\Http\Requests\Admin\RoleRequest;
use App\Http\Resources\Admin\RoleResource;
use App\Models\Role;
use App\Services\RoleService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * @group 角色管理
 *
 * 管理系统角色
 */
class RoleController extends Controller
{
    public function __construct(
        private RoleService $roleService
    ) {}

    /**
     * 获取角色列表
     *
     * @queryParam search string 搜索关键词（角色名称）. Example: admin
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page integer 每页条数. Example: 20
     *
     * @apiResourceCollection App\Http\Resources\Admin\RoleResource
     *
     * @apiResourceModel App\Models\Role paginate=20
     */
    public function index(Request $request)
    {
        $roles = $this->roleService->paginate(
            $request->get('per_page', 20),
            $request->get('search')
        );

        return RoleResource::collection($roles);
    }

    /**
     * 创建角色
     *
     * @bodyParam name string required 角色名称. Example: 管理员
     * @bodyParam description string 角色描述. Example: 系统管理员角色
     */
    public function store(RoleRequest $request)
    {
        $role = $this->roleService->create($request->validated());

        return (new RoleResource($role))->response()->setStatusCode(201);
    }

    /**
     * 获取角色详情
     *
     * @urlParam role integer required 角色ID. Example: 1
     */
    public function show(Role $role)
    {
        return new RoleResource($role);
    }

    /**
     * 更新角色
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @bodyParam name string required 角色名称. Example: 管理员
     * @bodyParam description string 角色描述. Example: 系统管理员角色
     */
    public function update(RoleRequest $request, Role $role)
    {
        $role = $this->roleService->update($role, $request->validated());

        return new RoleResource($role);
    }

    /**
     * 删除角色
     *
     * @urlParam role integer required 角色ID. Example: 1
     */
    public function destroy(Role $role)
    {
        $this->roleService->delete($role);

        return response()->noContent();
    }



    /**
     * 为角色分配菜单权限
     *
     * @urlParam role integer required 角色ID. Example: 1
     *
     * @bodyParam permissions array required 权限数组. Example: [{"menu_id": 2, "menu_permission_id": 1}]
     * @bodyParam permissions.*.menu_id integer required 菜单ID. Example: 1
     * @bodyParam permissions.*.menu_permission_id integer nullable 菜单权限ID，为空表示只有菜单访问权限. Example: 1
     *
     * @response {
     *   "message": "权限分配成功",
     *   "role": {
     *     "id": 1,
     *     "name": "管理员",
     *     "description": "系统管理员角色",
     *     "role_menu_permissions": [
     *       {
     *         "id": 1,
     *         "role_id": 1,
     *         "menu_id": 1,
     *         "menu_permission_id": null
     *       },
     *       {
     *         "id": 2,
     *         "role_id": 1,
     *         "menu_id": 2,
     *         "menu_permission_id": 1
     *       }
     *     ]
     *   }
     * }
     */
    public function assign(RoleMenuPermissionAssignRequest $request, Role $role): JsonResponse
    {
        $this->roleService->assignMenuPermissions($role, $request->validated('permissions'));

        return response()->json([
            'message' => '权限分配成功',
            'role' => new RoleResource($role->load('roleMenuPermissions'))
        ]);
    }
}
