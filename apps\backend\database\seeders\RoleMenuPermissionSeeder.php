<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\MenuPermission;
use App\Models\Role;
use App\Models\RoleMenuPermission;
use Illuminate\Database\Seeder;

class RoleMenuPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空表数据
        RoleMenuPermission::truncate();

        // 创建默认角色
        $adminRole = Role::firstOrCreate(
            ['name' => 'admin'],
            ['description' => '系统管理员']
        );

        $userRole = Role::firstOrCreate(
            ['name' => 'user'],
            ['description' => '普通用户']
        );

        // 获取所有菜单和权限
        $menus = Menu::with('permissions')->get();

        // 为管理员角色分配所有权限
        foreach ($menus as $menu) {
            // 分配菜单访问权限
            RoleMenuPermission::create([
                'role_id' => $adminRole->id,
                'menu_id' => $menu->id,
                'menu_permission_id' => null, // null表示菜单访问权限
            ]);

            // 分配所有菜单操作权限
            foreach ($menu->permissions as $permission) {
                RoleMenuPermission::create([
                    'role_id' => $adminRole->id,
                    'menu_id' => $menu->id,
                    'menu_permission_id' => $permission->id,
                ]);
            }
        }

        // 为普通用户角色分配基础权限
        $userMenus = [
            'Dashboard' => [], // 仪表盘只给访问权限
            'Console' => [], // 控制台只给访问权限
            'UserCenter' => [], // 个人中心只给访问权限
        ];

        foreach ($userMenus as $menuName => $permissions) {
            $menu = Menu::where('name', $menuName)->first();
            if ($menu) {
                // 分配菜单访问权限
                RoleMenuPermission::create([
                    'role_id' => $userRole->id,
                    'menu_id' => $menu->id,
                    'menu_permission_id' => null,
                ]);

                // 分配指定的操作权限
                foreach ($permissions as $authMark) {
                    $permission = MenuPermission::where('menu_id', $menu->id)
                        ->where('auth_mark', $authMark)
                        ->first();
                    
                    if ($permission) {
                        RoleMenuPermission::create([
                            'role_id' => $userRole->id,
                            'menu_id' => $menu->id,
                            'menu_permission_id' => $permission->id,
                        ]);
                    }
                }
            }
        }
    }
}
